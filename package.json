{"name": "png-converter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next", "clean:full": "./scripts/clean-build.sh --full", "fresh": "npm run clean && npm run dev"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-toast": "^1.2.6", "@types/lodash": "^4.17.16", "@vercel/analytics": "^1.5.0", "bmp-js": "^0.1.0", "browser-image-compression": "^2.0.2", "clsx": "^2.1.1", "framer-motion": "^12.6.3", "image-conversion": "^2.1.1", "jimp": "^1.6.0", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.4.1", "react-image-crop": "^11.0.10", "shadcn-ui": "^0.9.5", "sharp": "^0.34.0", "tailwind-merge": "^3.2.0", "to-ico": "^1.1.5", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.24.0", "@next/eslint-plugin-next": "^15.2.4", "@types/bmp-js": "^0.1.2", "@types/jszip": "^3.4.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "eslint": "^8.57.0", "eslint-config-next": "^15.2.4", "eslint-define-config": "^2.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}