# ImageTools.pro Troubleshooting Guide

## Common Build Issues

### 1. Lucide React Vendor Chunk Missing

**Error:** `ENOENT: no such file or directory, open '.next/server/vendor-chunks/lucide-react.js'`

**Cause:** Corrupted webpack build cache or import conflicts

**Solution:**
```bash
# Quick fix - clear Next.js cache
npm run clean
npm run dev

# Full fix - clear all caches and reinstall
npm run clean:full
```

### 2. Import Conflicts

**Error:** `Module parse failed: Identifier 'X' has already been declared`

**Cause:** Duplicate imports from the same package

**Solution:**
1. Check for duplicate imports in the file
2. Remove duplicates
3. Clear build cache: `npm run clean`

### 3. Build Cache Issues

**Symptoms:**
- Inconsistent compilation errors
- Missing vendor chunks
- Stale module references

**Solution:**
```bash
# Clear Next.js cache only
npm run clean

# Start fresh development server
npm run fresh

# Full cleanup (if above doesn't work)
npm run clean:full
```

## Prevention Best Practices

1. **Regular Cache Clearing**: Run `npm run clean` after major changes
2. **Import Management**: Avoid importing too many icons from lucide-react at once
3. **Incremental Testing**: Test builds after significant component changes
4. **Version Control**: Commit working states before major refactoring

## Quick Commands

- `npm run clean` - Remove .next directory
- `npm run fresh` - Clean and start dev server
- `npm run clean:full` - Full cleanup with dependency reinstall
- `./scripts/clean-build.sh` - Manual cleanup script
