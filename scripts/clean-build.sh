#!/bin/bash

# Clean Build Script for ImageTools.pro
# This script helps resolve build issues by clearing all caches and rebuilding

echo "🧹 Cleaning ImageTools.pro build artifacts..."

# Remove Next.js build cache
echo "Removing .next directory..."
rm -rf .next

# Remove node_modules (optional, only if needed)
if [ "$1" = "--full" ]; then
    echo "Removing node_modules..."
    rm -rf node_modules
    echo "Removing package-lock.json..."
    rm -rf package-lock.json
    echo "Reinstalling dependencies..."
    npm install
fi

echo "✅ Build cleanup complete!"
echo "You can now run 'npm run dev' to start fresh."
