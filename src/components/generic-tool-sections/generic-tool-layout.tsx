import React from 'react';
import {
    HowItWorksSection,
    BenefitsSection,
    FaqSection
} from '@/components/generic-tool-sections';
import { RelatedToolsSection } from '@/components/tool-sections/related-tools-section';
import { ToolHeroSection } from '@/components/layout/tool-hero-section';
import { BreadcrumbNavigation } from '@/components/layout/breadcrumb-navigation';

// Define the structure for props like steps, benefits, faqs if needed, 
// or use 'any' for simplicity if structure varies slightly or is complex.
// Using specific types is generally better for maintainability.
interface Step {
  title: string;
  description: string;
  icon?: React.ReactNode; // Make icon optional if not all steps have it
}

interface Benefit {
  title: string;
  description: string;
  icon?: React.ReactNode; // Make icon optional
}

interface Faq {
  question: string;
  answer: string;
}

interface RelatedTool {
  href: string;
  name: string;
  description: string;
  icon?: React.ReactNode; // Assuming related tools might have icons
}

interface GenericToolLayoutProps {
  ToolUI: React.ReactNode;
  toolName: string;
  toolDescription: string;
  steps: Step[];
  benefits: Benefit[];
  faqs: Faq[];
  relatedTools: RelatedTool[];
  badge?: React.ReactNode;
  featureHighlights?: React.ReactNode;
  showHowItWorks?: boolean; // Optional prop to show/hide sections
  showBenefits?: boolean;   // Optional prop
  showFaq?: boolean;        // Optional prop
}

export function GenericToolLayout({ 
  ToolUI, 
  toolName,
  toolDescription,
  steps, 
  benefits, 
  faqs, 
  relatedTools,
  badge,
  featureHighlights,
  showHowItWorks = true, // Default to true
  showBenefits = true,   // Default to true
  showFaq = true         // Default to true
}: GenericToolLayoutProps) {
  return (
    <>
      {/* Breadcrumb Navigation */}
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 pt-4">
        <BreadcrumbNavigation />
      </div>

      <ToolHeroSection
        title={toolName}
        description={toolDescription}
        badge={badge}
        featureHighlights={featureHighlights}
      >
      {ToolUI}
      </ToolHeroSection>

      <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="space-y-12">
          {showHowItWorks && <HowItWorksSection toolName={toolName} steps={steps} />}
          {showBenefits && <BenefitsSection toolName={toolName} benefits={benefits} />}
          {showFaq && <FaqSection toolName={toolName} faqs={faqs} />}
          <RelatedToolsSection relatedTools={relatedTools} />
        </div>
      </div>
    </>
  );
}

// Optional: Export types if they might be reused elsewhere
export type { Step, Benefit, Faq, RelatedTool }; 