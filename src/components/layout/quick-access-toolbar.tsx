"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  FileImage,
  Minimize2,
  Scissors,
  Crop,
  RotateCcw,
  Palette
} from 'lucide-react';

const quickAccessTools = [
  {
    name: 'Convert',
    href: '/convert/png-to-jpg',
    icon: <FileImage className="h-4 w-4" />,
    description: 'Convert image formats'
  },
  {
    name: 'Compress',
    href: '/compress-image',
    icon: <Minimize2 className="h-4 w-4" />,
    description: 'Reduce file size'
  },
  {
    name: 'Resize',
    href: '/resize-image',
    icon: <Scissors className="h-4 w-4" />,
    description: 'Change dimensions'
  },
  {
    name: 'Crop',
    href: '/crop-image',
    icon: <Crop className="h-4 w-4" />,
    description: 'Trim images'
  },
  {
    name: 'Rotate',
    href: '/rotate-flip-image',
    icon: <RotateCcw className="h-4 w-4" />,
    description: 'Rotate & flip'
  },
  {
    name: 'Favicon',
    href: '/favicon-maker',
    icon: <Palette className="h-4 w-4" />,
    description: 'Create favicons'
  }
];

interface QuickAccessToolbarProps {
  className?: string;
}

export function QuickAccessToolbar({ className }: QuickAccessToolbarProps) {
  const pathname = usePathname();

  return (
    <div className={cn(
      "flex items-center gap-1 p-2 bg-background/80 backdrop-blur-sm border border-border rounded-lg shadow-sm",
      className
    )}>
      {quickAccessTools.map((tool) => (
        <Link key={tool.href} href={tool.href}>
          <Button
            variant={pathname === tool.href ? "default" : "ghost"}
            size="sm"
            className="flex items-center gap-2 h-8 px-3"
            title={tool.description}
          >
            {tool.icon}
            <span className="hidden sm:inline text-xs">{tool.name}</span>
          </Button>
        </Link>
      ))}
    </div>
  );
}
