"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { ChevronRight, Home } from 'lucide-react';
import { navigationCategories } from './sidebar-navigation';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

export function BreadcrumbNavigation({ className }: { className?: string }) {
  const pathname = usePathname();

  // Don't show breadcrumbs on home page
  if (pathname === '/') return null;

  const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', href: '/' }
  ];

  // Find current tool and its category
  const allTools = navigationCategories.flatMap(cat => 
    cat.tools.map(tool => ({ ...tool, category: cat.name }))
  );
  
  const currentTool = allTools.find(tool => tool.href === pathname);

  if (currentTool) {
    // Add category if it's not "Popular Tools"
    if (currentTool.category !== 'Popular Tools') {
      breadcrumbs.push({ 
        label: currentTool.category,
        href: '/all-tools' // Could be enhanced to filter by category
      });
    }
    
    // Add current tool
    breadcrumbs.push({ label: currentTool.name });
  } else {
    // Handle special pages
    if (pathname === '/all-tools') {
      breadcrumbs.push({ label: 'All Tools' });
    } else if (pathname.startsWith('/convert/')) {
      breadcrumbs.push({ label: 'Format Conversion', href: '/all-tools' });
      breadcrumbs.push({ label: 'Convert Image' });
    } else if (pathname.startsWith('/compress/')) {
      breadcrumbs.push({ label: 'Optimize & Compress', href: '/all-tools' });
      breadcrumbs.push({ label: 'Compress Image' });
    } else if (pathname.startsWith('/resize/')) {
      breadcrumbs.push({ label: 'Edit & Transform', href: '/all-tools' });
      breadcrumbs.push({ label: 'Resize Image' });
    } else {
      // Generic fallback
      const segments = pathname.split('/').filter(Boolean);
      segments.forEach((segment, index) => {
        const isLast = index === segments.length - 1;
        breadcrumbs.push({
          label: segment.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' '),
          href: isLast ? undefined : `/${segments.slice(0, index + 1).join('/')}`
        });
      });
    }
  }

  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}>
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
          )}
          {item.href ? (
            <Link 
              href={item.href}
              className="hover:text-foreground transition-colors flex items-center gap-1"
            >
              {index === 0 && <Home className="h-3 w-3" />}
              {item.label}
            </Link>
          ) : (
            <span className="text-foreground font-medium flex items-center gap-1">
              {index === 0 && <Home className="h-3 w-3" />}
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}
