import Link from "next/link";
import { Heart, ArrowUpRight, FileImage, MessageSquare, Shield, Image, File, Palette, ArrowRight, Layers, RotateCcw, Scissors, Square, Minimize2, Crop as CropIcon, Wrench } from "lucide-react";
import { Logo } from "@/components/ui/logo";
import { But<PERSON> } from "@/components/ui/button";

// Updated tool categories for footer
const popularToolCategories = [
  {
    name: "Conversion",
    icon: <FileImage className="h-4 w-4 text-primary" />,
    tools: [
      { name: "PNG to JPG", href: "/convert/png-to-jpg" },
      { name: "JPG to WebP", href: "/convert/jpg-to-webp" },
      { name: "WebP to PNG", href: "/convert/webp-to-png" },
      { name: "PNG to PDF", href: "/convert/png-to-pdf" },
    ]
  },
  {
    name: "Resizing",
    icon: <Scissors className="h-4 w-4 text-primary" />,
    tools: [
      { name: "Resize JPG", href: "/resize/jpg" },
      { name: "Resize PNG", href: "/resize/png" },
      { name: "Resize WebP", href: "/resize/webp" },
    ]
  },
  {
    name: "Compression",
    icon: <Minimize2 className="h-4 w-4 text-primary" />,
    tools: [
      { name: "Compress JPG", href: "/compress/jpg" },
      { name: "Compress PNG", href: "/compress/png" },
      { name: "Compress WebP", href: "/compress/webp" },
    ]
  },
  {
    name: "Special Tools",
    icon: <Palette className="h-4 w-4 text-primary" />,
    tools: [
      { name: "Crop Image", href: "/crop-image" },
      { name: "Favicon Maker", href: "/favicon-maker" },
      { name: "Placeholder Maker", href: "/placeholder-maker" },
      { name: "Image Compositor", href: "/composite-images" },
      { name: "Rotate & Flip", href: "/rotate-flip-image" },
      { name: "Image Filters", href: "/adjust-image" },
      { name: "Add Watermark", href: "/add-watermark" },
      { name: "Base64 Converter", href: "/encode/image-to-base64" },
    ]
  }
];

const companyLinks = [
  { name: "About", href: "/about" },
  { name: "Contact", href: "/contact" },
  { name: "FAQ", href: "/faq" },
];

const legalLinks = [
  { name: "Privacy Policy", href: "/privacy" },
  { name: "Terms of Service", href: "/terms" },
  { name: "Cookie Policy", href: "/cookies" },
];

export function Footer() {
  return (
    <footer className="border-t border-border/40 bg-gradient-to-b from-background to-accent/5 pt-16 pb-8">
      <div className="container max-w-[1440px] px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8 lg:gap-12 mb-16">
          {/* Column 1: Logo and Description */}
          <div className="md:col-span-4 lg:col-span-3">
            <div className="mb-6">
              <Logo />
            </div>
            <p className="text-muted-foreground text-sm leading-relaxed mb-6">
              Your complete online toolkit for image conversion, editing, resizing, compression, and more.
            </p>

            {/* Trust Badge */}
            <div className="flex items-center gap-2 bg-primary/5 rounded-lg p-3 border border-primary/10">
              <Shield className="h-5 w-5 text-primary" />
              <span className="text-xs font-medium">Secure & Privacy-Focused Processing</span>
            </div>
          </div>

          {/* Column 2: Popular Tools */}
          <div className="md:col-span-8 lg:col-span-5">
            <h3 className="font-semibold text-base mb-6 flex items-center">
              <Wrench className="h-4 w-4 mr-2 text-primary" />
              <span>Popular Tools</span>
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-x-6 gap-y-8">
              {popularToolCategories.map((category) => (
                <div key={category.name}>
                  <h4 className="text-sm font-medium mb-3 flex items-center">
                    {category.icon}
                    <span className="ml-2">{category.name}</span>
                  </h4>
                  <ul className="space-y-2">
                    {category.tools.map((tool) => (
                      <li key={tool.href}>
                        <Link
                          href={tool.href}
                          className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center group"
                        >
                          <span>{tool.name}</span>
                          <ArrowUpRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            <div className="mt-8">
              <Link href="/all-tools">
                <Button variant="outline" size="sm" className="w-full sm:w-auto">
                  <span>View All Tools</span>
                  <ArrowRight className="ml-2 h-3.5 w-3.5" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Column 3 & 4: Company & Legal */}
          <div className="md:col-span-6 lg:col-span-2">
            <h3 className="font-semibold text-base mb-6 flex items-center">
              <MessageSquare className="h-4 w-4 mr-2 text-primary" />
              <span>Company</span>
            </h3>
            <ul className="space-y-4">
              {companyLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center group"
                  >
                    <span>{link.name}</span>
                    <ArrowUpRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="md:col-span-6 lg:col-span-2">
            <h3 className="font-semibold text-base mb-6 flex items-center">
              <Shield className="h-4 w-4 mr-2 text-primary" />
              <span>Legal</span>
            </h3>
            <ul className="space-y-4">
              {legalLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center group"
                  >
                    <span>{link.name}</span>
                    <ArrowUpRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Copyright and Love Message */}
        <div className="border-t border-border/40 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="text-center md:text-left text-sm text-muted-foreground">
            &copy; 2025 ImageTools.Pro. All rights reserved. New service - launched 2025.
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <span className="flex items-center">
              Made with <Heart className="h-4 w-4 text-destructive mx-1 fill-destructive inline-block align-middle animate-pulse" /> for fast & secure image conversions
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
}
