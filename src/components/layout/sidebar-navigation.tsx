"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  ChevronDown, 
  ChevronRight,
  X,
  Menu,
  Star,
  Clock,
  FileImage,
  Minimize2,
  Scissors,
  Palette,
  Binary,
  Layers,
  RotateCcw,
  Square,
  SlidersHorizontal,
  PenTool,
  Crop,
  Image,
  FileText,
  File
} from 'lucide-react';

// Enhanced tool categorization for better UX
export interface NavigationTool {
  name: string;
  href: string;
  icon: React.ReactNode;
  description: string;
  isPopular?: boolean;
  isNew?: boolean;
}

export interface NavigationCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  tools: NavigationTool[];
  isExpanded?: boolean;
}

// Reorganized categories for better user experience
export const navigationCategories: NavigationCategory[] = [
  {
    id: 'popular',
    name: 'Popular Tools',
    icon: <Star className="h-4 w-4" />,
    tools: [
      {
        name: 'Convert PNG to JPG',
        href: '/convert/png-to-jpg',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert PNG to JPG format',
        isPopular: true
      },
      {
        name: 'Compress Image',
        href: '/compress-image',
        icon: <Minimize2 className="h-4 w-4" />,
        description: 'Reduce image file size',
        isPopular: true
      },
      {
        name: 'Resize Image',
        href: '/resize-image',
        icon: <Scissors className="h-4 w-4" />,
        description: 'Change image dimensions',
        isPopular: true
      },
      {
        name: 'Crop Image',
        href: '/crop-image',
        icon: <Crop className="h-4 w-4" />,
        description: 'Crop and trim images',
        isPopular: true
      }
    ]
  },
  {
    id: 'convert',
    name: 'Format Conversion',
    icon: <FileImage className="h-4 w-4" />,
    tools: [
      // PNG Conversions
      {
        name: 'PNG to JPG',
        href: '/convert/png-to-jpg',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert PNG to JPG format'
      },
      {
        name: 'PNG to WebP',
        href: '/convert/png-to-webp',
        icon: <Image className="h-4 w-4" />,
        description: 'Convert PNG to WebP format'
      },
      {
        name: 'PNG to AVIF',
        href: '/convert/png-to-avif',
        icon: <File className="h-4 w-4" />,
        description: 'Convert PNG to AVIF format'
      },
      {
        name: 'PNG to PDF',
        href: '/convert/png-to-pdf',
        icon: <FileText className="h-4 w-4" />,
        description: 'Convert PNG to PDF document'
      },
      // JPG Conversions
      {
        name: 'JPG to PNG',
        href: '/convert/jpg-to-png',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert JPG to PNG format'
      },
      {
        name: 'JPG to WebP',
        href: '/convert/jpg-to-webp',
        icon: <Image className="h-4 w-4" />,
        description: 'Convert JPG to WebP format'
      },
      {
        name: 'JPG to AVIF',
        href: '/convert/jpg-to-avif',
        icon: <File className="h-4 w-4" />,
        description: 'Convert JPG to AVIF format'
      },
      // WebP Conversions
      {
        name: 'WebP to PNG',
        href: '/convert/webp-to-png',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert WebP to PNG format'
      },
      {
        name: 'WebP to JPG',
        href: '/convert/webp-to-jpg',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert WebP to JPG format'
      },
      {
        name: 'WebP to AVIF',
        href: '/convert/webp-to-avif',
        icon: <File className="h-4 w-4" />,
        description: 'Convert WebP to AVIF format'
      },
      // AVIF Conversions
      {
        name: 'AVIF to PNG',
        href: '/convert/avif-to-png',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert AVIF to PNG format'
      },
      {
        name: 'AVIF to JPG',
        href: '/convert/avif-to-jpg',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert AVIF to JPG format'
      },
      {
        name: 'AVIF to WebP',
        href: '/convert/avif-to-webp',
        icon: <Image className="h-4 w-4" />,
        description: 'Convert AVIF to WebP format'
      },
      // TIFF Conversions
      {
        name: 'TIFF to PNG',
        href: '/convert/tiff-to-png',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert TIFF to PNG format'
      },
      {
        name: 'TIFF to JPG',
        href: '/convert/tiff-to-jpg',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert TIFF to JPG format'
      },
      // BMP Conversions
      {
        name: 'BMP to PNG',
        href: '/convert/bmp-to-png',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert BMP to PNG format'
      },
      {
        name: 'BMP to JPG',
        href: '/convert/bmp-to-jpg',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Convert BMP to JPG format'
      }
    ]
  },
  {
    id: 'optimize',
    name: 'Optimize & Compress',
    icon: <Minimize2 className="h-4 w-4" />,
    tools: [
      {
        name: 'Compress Any Image',
        href: '/compress-image',
        icon: <Minimize2 className="h-4 w-4" />,
        description: 'Compress images of any format'
      },
      {
        name: 'Compress PNG',
        href: '/compress/png',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Optimize PNG file size'
      },
      {
        name: 'Compress JPG',
        href: '/compress/jpg',
        icon: <FileImage className="h-4 w-4" />,
        description: 'Optimize JPG file size'
      },
      {
        name: 'Compress WebP',
        href: '/compress/webp',
        icon: <Image className="h-4 w-4" />,
        description: 'Optimize WebP file size'
      }
    ]
  },
  {
    id: 'edit',
    name: 'Edit & Transform',
    icon: <Palette className="h-4 w-4" />,
    tools: [
      {
        name: 'Resize Image',
        href: '/resize-image',
        icon: <Scissors className="h-4 w-4" />,
        description: 'Change image dimensions'
      },
      {
        name: 'Crop Image',
        href: '/crop-image',
        icon: <Crop className="h-4 w-4" />,
        description: 'Crop and trim images'
      },
      {
        name: 'Rotate & Flip',
        href: '/rotate-flip-image',
        icon: <RotateCcw className="h-4 w-4" />,
        description: 'Rotate and flip images'
      },
      {
        name: 'Image Filters',
        href: '/adjust-image',
        icon: <SlidersHorizontal className="h-4 w-4" />,
        description: 'Apply filters and adjustments'
      },
      {
        name: 'Add Watermark',
        href: '/add-watermark',
        icon: <PenTool className="h-4 w-4" />,
        description: 'Add text watermarks'
      },
      {
        name: 'Image Compositor',
        href: '/composite-images',
        icon: <Layers className="h-4 w-4" />,
        description: 'Merge multiple images'
      }
    ]
  },
  {
    id: 'generate',
    name: 'Generate & Create',
    icon: <Square className="h-4 w-4" />,
    tools: [
      {
        name: 'Favicon Maker',
        href: '/favicon-maker',
        icon: <Palette className="h-4 w-4" />,
        description: 'Create website favicons'
      },
      {
        name: 'Placeholder Maker',
        href: '/placeholder-maker',
        icon: <Square className="h-4 w-4" />,
        description: 'Generate placeholder images'
      }
    ]
  },
  {
    id: 'encode',
    name: 'Encode & Decode',
    icon: <Binary className="h-4 w-4" />,
    tools: [
      {
        name: 'Base64 Converter',
        href: '/encode/image-to-base64',
        icon: <Binary className="h-4 w-4" />,
        description: 'Convert images to/from Base64'
      }
    ]
  }
];

interface SidebarNavigationProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

export function SidebarNavigation({ isOpen, onToggle, className }: SidebarNavigationProps) {
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['popular', 'convert']) // Default expanded categories
  );
  const [recentTools, setRecentTools] = useState<NavigationTool[]>([]);

  // Load recent tools from localStorage
  useEffect(() => {
    const stored = localStorage.getItem('imagetools-recent');
    if (stored) {
      try {
        setRecentTools(JSON.parse(stored));
      } catch (e) {
        console.error('Failed to parse recent tools:', e);
      }
    }
  }, []);

  // Add current tool to recent tools
  useEffect(() => {
    if (pathname && pathname !== '/') {
      const allTools = navigationCategories.flatMap(cat => cat.tools);
      const currentTool = allTools.find(tool => tool.href === pathname);
      
      if (currentTool) {
        setRecentTools(prev => {
          const filtered = prev.filter(tool => tool.href !== currentTool.href);
          const updated = [currentTool, ...filtered].slice(0, 5);
          localStorage.setItem('imagetools-recent', JSON.stringify(updated));
          return updated;
        });
      }
    }
  }, [pathname]);

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  // Filter tools based on search query
  const filteredCategories = searchQuery.trim() 
    ? navigationCategories.map(category => ({
        ...category,
        tools: category.tools.filter(tool => 
          tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          tool.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })).filter(category => category.tools.length > 0)
    : navigationCategories;

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <aside className={cn(
        "fixed left-0 bg-background border-r border-border z-50 transition-transform duration-300 ease-in-out",
        "w-80 flex flex-col",
        // Mobile: full height, Desktop: below header
        "top-0 h-full lg:top-20 lg:h-[calc(100vh-5rem)]",
        isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border lg:hidden">
          <h2 className="font-semibold text-lg">Image Tools</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="h-10 w-10"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-border">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tools..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        {/* Navigation Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-2">
            {/* Recent Tools */}
            {!searchQuery && recentTools.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center gap-2 px-2 py-1 mb-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">Recent</span>
                </div>
                <div className="space-y-1">
                  {recentTools.map((tool) => (
                    <Link
                      key={tool.href}
                      href={tool.href}
                      onClick={() => onToggle()}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
                        "hover:bg-accent hover:text-accent-foreground",
                        pathname === tool.href
                          ? "bg-primary text-primary-foreground"
                          : "text-foreground"
                      )}
                    >
                      {tool.icon}
                      <span className="flex-1 truncate">{tool.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Tool Categories */}
            {filteredCategories.map((category) => (
              <div key={category.id} className="mb-2">
                <Button
                  variant="ghost"
                  onClick={() => toggleCategory(category.id)}
                  className="w-full justify-between px-2 py-1 h-auto font-medium text-sm"
                >
                  <div className="flex items-center gap-2">
                    {category.icon}
                    <span>{category.name}</span>
                    {category.id === 'popular' && (
                      <Badge variant="secondary" className="text-xs">
                        {category.tools.length}
                      </Badge>
                    )}
                  </div>
                  {expandedCategories.has(category.id) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>

                {(expandedCategories.has(category.id) || searchQuery) && (
                  <div className="ml-2 mt-1 space-y-1">
                    {category.tools.map((tool) => (
                      <Link
                        key={tool.href}
                        href={tool.href}
                        onClick={() => onToggle()}
                        className={cn(
                          "flex items-center gap-3 px-3 py-3 rounded-md text-sm transition-colors",
                          "hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
                          "touch-manipulation", // Better touch interactions
                          pathname === tool.href
                            ? "bg-primary text-primary-foreground"
                            : "text-muted-foreground hover:text-foreground"
                        )}
                      >
                        {tool.icon}
                        <div className="flex-1 min-w-0">
                          <div className="truncate">{tool.name}</div>
                          {searchQuery && (
                            <div className="text-xs text-muted-foreground truncate">
                              {tool.description}
                            </div>
                          )}
                        </div>
                        {tool.isNew && (
                          <Badge variant="secondary" className="text-xs">New</Badge>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}

            {/* No Results */}
            {searchQuery && filteredCategories.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No tools found</p>
                <p className="text-xs">Try a different search term</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-border">
          <Link
            href="/all-tools"
            onClick={() => onToggle()}
            className="flex items-center justify-center gap-2 w-full py-2 px-3 rounded-md bg-primary/10 text-primary hover:bg-primary/20 transition-colors text-sm font-medium"
          >
            <FileImage className="h-4 w-4" />
            View All Tools
          </Link>
        </div>
      </aside>
    </>
  );
}
