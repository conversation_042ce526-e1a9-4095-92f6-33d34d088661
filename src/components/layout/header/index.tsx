"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { Menu, X } from "lucide-react";
import { Logo } from "@/components/ui/logo";
import { DesktopNavigation } from "./desktop-navigation";
import { MobileMenu } from "./mobile-menu";
import { ThemeToggle } from "@/components/theme-toggle";

export function Header() {
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [scrolled]);

  // Show simplified header on tool pages (when sidebar is present)
  const isToolPage = pathname !== '/';

  return (
    <header className={`sticky top-0 z-50 w-full border-b transition-all duration-300 ${scrolled ? 'border-border/40 bg-background/80 backdrop-blur-lg shadow-sm' : 'border-transparent bg-background/0'}`}>
      <div className="container max-w-[1440px] flex h-20 items-center justify-between px-4 sm:px-6 lg:px-8">
        <div className="flex items-center gap-4">
          <Logo />
        </div>

        {/* Show full navigation only on home page */}
        {!isToolPage && <DesktopNavigation />}

        {/* Show simplified navigation on tool pages */}
        {isToolPage && (
          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
          </div>
        )}

        {/* Mobile Menu - always show */}
        <MobileMenu />
      </div>
    </header>
  );
}
