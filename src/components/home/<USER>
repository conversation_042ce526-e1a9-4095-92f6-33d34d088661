import Link from "next/link";
import { <PERSON><PERSON><PERSON>, FileImage, Shield, Zap, Image, CheckCircle, Wrench } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export function HeroSection() {
  return (
    <section className="w-full py-12 md:py-24 bg-gradient-to-b from-background via-accent/30 to-background relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute -top-[30%] left-[10%] w-[80%] h-[60%] bg-primary/5 dark:bg-primary/10 rounded-full blur-[120px]"></div>
        <div className="absolute top-[60%] left-[20%] w-[60%] h-[50%] bg-indigo-500/5 dark:bg-indigo-900/30 rounded-full blur-[100px]"></div>
        <div className="absolute top-[20%] left-[30%] w-2 h-2 bg-primary rounded-full shadow-[0_0_10px_4px_rgba(124,58,237,0.3)] dark:shadow-[0_0_10px_4px_rgba(124,58,237,0.15)]"></div>
        <div className="absolute top-[60%] left-[70%] w-3 h-3 bg-indigo-500 rounded-full shadow-[0_0_10px_4px_rgba(99,102,241,0.3)] dark:shadow-[0_0_10px_4px_rgba(49,46,129,0.15)]"></div>
        <div className="absolute top-[30%] left-[50%] w-1.5 h-1.5 bg-primary rounded-full shadow-[0_0_10px_4px_rgba(124,58,237,0.2)] dark:shadow-[0_0_10px_4px_rgba(124,58,237,0.10)]"></div>
      </div>

      <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex flex-col items-center text-center">
          <div className="flex flex-col space-y-8 max-w-3xl mx-auto">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary/90 text-sm font-medium mb-2 mx-auto animate-pulse-subtle">
              <span className="flex items-center gap-1.5">
                <span className="h-1.5 w-1.5 rounded-full bg-primary dark:bg-primary/90"></span>
                <span>100% Free</span>
              </span>
              <span className="h-4 w-px bg-primary/30 dark:bg-primary/40"></span>
              <span>No Sign-up Required</span>
            </div>

            {/* Heading */}
            <div>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl xl:text-6xl/none mb-6">
                <span className="text-gradient">Your All-in-One</span>
                <span className="block mt-3">Image <span className="relative inline-block">
                  <span className="relative z-10">Toolkit</span>
                  <span className="absolute bottom-1 left-0 w-full h-3 bg-primary/20 rounded-lg -z-10"></span>
                </span></span>
              </h1>
              <p className="text-muted-foreground text-lg md:text-xl max-w-[700px] mx-auto leading-relaxed">
                Easily convert, compress, resize, crop, and edit your images online. Fast, secure, and free image processing tools right in your browser.
              </p>
            </div>

            {/* Feature list */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2 max-w-3xl mx-auto">
              <div className="flex flex-col items-center gap-2 bg-background/50 backdrop-blur-sm rounded-lg p-4 border border-accent/20 shadow-sm">
                <div className="bg-primary/10 rounded-full p-2">
                  <Zap className="h-5 w-5 text-primary" />
                </div>
                <span className="text-sm font-medium text-center">Fast Processing</span>
              </div>
              <div className="flex flex-col items-center gap-2 bg-background/50 backdrop-blur-sm rounded-lg p-4 border border-accent/20 shadow-sm">
                <div className="bg-primary/10 rounded-full p-2">
                  <Shield className="h-5 w-5 text-primary" />
                </div>
                <span className="text-sm font-medium text-center">Secure & Private</span>
              </div>
              <div className="flex flex-col items-center gap-2 bg-background/50 backdrop-blur-sm rounded-lg p-4 border border-accent/20 shadow-sm">
                <div className="bg-primary/10 rounded-full p-2">
                  <Wrench className="h-5 w-5 text-primary" />
                </div>
                <span className="text-sm font-medium text-center">Multiple Tools</span>
              </div>
              <div className="flex flex-col items-center gap-2 bg-background/50 backdrop-blur-sm rounded-lg p-4 border border-accent/20 shadow-sm">
                <div className="bg-primary/10 rounded-full p-2">
                  <CheckCircle className="h-5 w-5 text-primary" />
                </div>
                <span className="text-sm font-medium text-center">Easy to Use</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-8 max-w-xl mx-auto w-full">
              <Link href="/all-tools" className="flex-1">
                <Button size="lg" className="w-full btn-primary py-6 rounded-xl group">
                  <Wrench className="mr-2 h-5 w-5" />
                  <span>Explore All Tools</span>
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              <Link href="#tools" className="flex-1">
                <Button size="lg" variant="outline" className="w-full py-6 rounded-xl border-2 border-primary/20 hover:bg-primary/5 transition-all duration-300">
                  <Image className="mr-2 h-5 w-5" aria-label="Featured tools icon" />
                  <span>View Featured Tools</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}