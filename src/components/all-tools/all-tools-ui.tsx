"use client";

import Link from "next/link";
import { ArrowRight, FileImage, FileText, Palette, FileIcon, Image, File, Search, Shield, UserX, Star, Shrink, Crop as CropIcon, Layers, Square, Wrench, Scissors } from "lucide-react";
import { useState, ReactNode } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toolCategories, allTools } from "@/data/tools";
import { Tool, ToolCategory } from "@/types/tools";
import React from "react";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Define the AllToolsUI component
export function AllToolsUI() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");

  // Filter tools based on search query and active category
  const filteredTools = searchQuery.trim() !== ""
    ? allTools.filter((tool: Tool) =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : activeCategory === "all"
      ? allTools
      : toolCategories.find((cat: ToolCategory) => cat.name === activeCategory)?.tools || [];

  // Group tools by category for better organization
  const groupedTools = activeCategory === "all" && !searchQuery.trim()
    ? toolCategories.map(category => ({
        category,
        tools: category.tools
      }))
    : [{
        category: { name: activeCategory === "all" ? "Search Results" : activeCategory, icon: null, tools: [] },
        tools: filteredTools
      }];

  return (
    <div>
      {/* Hero Section */}
      <div className="bg-gradient-to-b from-background via-accent/5 to-background py-16 md:py-20 mb-8">
        <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl xl:text-6xl/none mb-6">Complete <span className="text-gradient">Image Toolkit</span></h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto mb-8">
            Explore our complete collection of free online image tools. Convert, compress, resize, crop, edit, and generate images easily.
          </p>

          {/* Search Bar - Centered and Prominent */}
          <div className="max-w-xl mx-auto mb-8 relative">
            <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search 35+ image tools..."
              className="w-full pl-12 pr-4 py-3 bg-background dark:bg-background/80 rounded-full text-base focus:outline-none focus:ring-2 focus:ring-primary/40 border border-accent/50 shadow-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Filter Buttons - Centered */}
          <div className="flex flex-wrap gap-2 justify-center max-w-4xl mx-auto">
            <Button
              variant={activeCategory === "all" ? "default" : "outline"}
              size="sm"
              className="rounded-full px-3 text-xs sm:text-sm sm:px-4 flex items-center gap-1.5"
              onClick={() => setActiveCategory("all")}
            >
              <Wrench className="h-3 w-3" />
              All Tools
            </Button>
            {toolCategories.map((category: ToolCategory) => (
              <Button
                key={category.name}
                variant={activeCategory === category.name ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveCategory(category.name)}
                className="flex items-center gap-1.5 rounded-full px-3 text-xs sm:text-sm sm:px-4"
              >
                {category.icon && React.cloneElement(category.icon, { className: "h-4 w-4 mr-2" })}
                <span>{category.name}</span>
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tools by Category */}
        {groupedTools.map((group, groupIndex) => (
          <div key={groupIndex} className="mb-12">
            {/* Category Header */}
            {group.category.name !== "Search Results" && activeCategory === "all" && !searchQuery.trim() && (
              <div className="flex items-center gap-3 mb-6">
                {group.category.icon && React.cloneElement(group.category.icon, { className: "h-6 w-6 text-primary" })}
                <h2 className="text-2xl font-bold">{group.category.name}</h2>
                <Badge variant="secondary" className="text-sm">
                  {group.tools.length} tools
                </Badge>
              </div>
            )}

            {/* Tools Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {group.tools.length > 0 ? (
                group.tools.map((tool: Tool) => (
                  <Link key={tool.href} href={tool.href} className="group">
                    <div className="bg-gradient-card rounded-xl overflow-hidden shadow-card border border-accent/30 h-full card-hover transition-all duration-300 hover:shadow-lg hover:scale-[1.02] hover:border-primary/30">
                      <div className="p-6">
                        {/* Tool Icon */}
                        <div className="rounded-xl bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                          {tool.icon && React.cloneElement(tool.icon, { className: "h-8 w-8" })}
                        </div>

                        {/* Tool Header */}
                        <div className="mb-3">
                          <h3 className="text-xl font-bold group-hover:text-primary transition-colors">{tool.name}</h3>
                        </div>

                        {/* Tool Description */}
                        <p className="text-muted-foreground mb-4 text-sm leading-relaxed">
                          {tool.description}
                        </p>

                        {/* Call to Action */}
                        <div className="flex items-center text-primary font-medium">
                          Try Now <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="col-span-1 md:col-span-2 lg:col-span-3 text-center py-12">
                  <FileImage className="h-16 w-16 text-muted-foreground/50 mx-auto mb-4" />
                  <h3 className="text-xl font-medium mb-2">No tools found</h3>
                  <p className="text-muted-foreground">
                    No tools match your search criteria. Try a different search term or category.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => {
                      setSearchQuery("");
                      setActiveCategory("all");
                    }}
                  >
                    Clear filters
                  </Button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 