import type { Metadata } from 'next';

// Define metadata for the homepage
export const metadata: Metadata = {
  title: 'ImageTools.pro: Free Online Image Conversion, Compression & Editing Tools',
  description: 'Your ultimate online toolkit for images. Convert, compress, resize, crop, composite, rotate, and flip images—all for free online.',
  keywords: [
    'image tools', 'online image editor', 'image converter', 'image compressor',
    'image resizer', 'crop image', 'favicon maker', 'placeholder generator', 'free image tools'
  ],
  openGraph: {
    title: 'ImageTools.pro: Free Online Image Conversion, Compression & Editing Tools',
    description: 'The ultimate free online toolkit for all your image processing needs.',
  },
};

import { FeatureHighlightsBar } from "@/components/home/<USER>";
import { HeroSection } from "@/components/home/<USER>";
import dynamic from 'next/dynamic';

// Dynamically import sections likely below the fold
const DynamicToolsSection = dynamic(() => 
  import('@/components/home/<USER>').then(mod => mod.ToolsSection),
  { loading: () => <div className="h-96 w-full animate-pulse bg-muted/50 rounded-lg" /> } 
);
const DynamicWhyChooseUsSection = dynamic(() => 
  import('@/components/home/<USER>').then(mod => mod.WhyChooseUsSection),
  { loading: () => <div className="h-96 w-full animate-pulse bg-muted/50 rounded-lg" /> } 
);
const DynamicHowItWorksSection = dynamic(() => 
  import('@/components/home/<USER>').then(mod => mod.HowItWorksSection),
  { loading: () => <div className="h-96 w-full animate-pulse bg-muted/50 rounded-lg" /> } 
);
const DynamicAboutSection = dynamic(() => 
  import('@/components/home/<USER>').then(mod => mod.AboutSection),
  { loading: () => <div className="h-96 w-full animate-pulse bg-muted/50 rounded-lg" /> } 
);
const DynamicCtaSection = dynamic(() => 
  import('@/components/home/<USER>').then(mod => mod.CtaSection),
  { loading: () => <div className="h-96 w-full animate-pulse bg-muted/50 rounded-lg" /> } 
);

export default function Home() {
  return (
    <div className="flex flex-col min-h-[calc(100vh-5rem)] bg-background dark:bg-gray-900">
      {/* Sections likely above the fold */}
      <HeroSection />
      <FeatureHighlightsBar />

      {/* Container sections loaded dynamically */}
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 space-y-24 py-12">
        <DynamicToolsSection />
        <DynamicWhyChooseUsSection />
        <DynamicHowItWorksSection />
        <DynamicAboutSection />
        <DynamicCtaSection />
      </div>
    </div>
  );
}
